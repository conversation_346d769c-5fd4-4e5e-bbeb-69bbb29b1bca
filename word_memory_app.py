#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式单词记忆程序
使用rich库创建美观的终端界面
"""

import json
import random
import os
from typing import List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.prompt import Prompt, IntPrompt
from rich.layout import Layout
from rich.align import Align
from rich import box
from rich.progress import Progress, BarColumn, TextColumn

class WordMemoryApp:
    def __init__(self, data_file: str = "vocabulary_data.json"):
        self.console = Console()
        self.data_file = data_file
        self.words = []
        self.progress_data = {}  # 存储学习进度
        self.current_word = None
        self.load_data()
        
    def load_data(self):
        """加载单词数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.words = json.load(f)
            self.console.print(f"[green]成功加载 {len(self.words)} 个单词[/green]")
            
            # 初始化进度数据
            for word in self.words:
                word_key = word['english']
                if word_key not in self.progress_data:
                    self.progress_data[word_key] = {
                        'correct_streak': 0,  # 连续答对次数
                        'required_streak': 2,  # 需要连续答对的次数
                        'total_attempts': 0,   # 总尝试次数
                        'correct_count': 0,    # 答对次数
                        'mastered': False      # 是否已掌握
                    }
        except FileNotFoundError:
            self.console.print(f"[red]错误：找不到文件 {self.data_file}[/red]")
            exit(1)
        except json.JSONDecodeError:
            self.console.print(f"[red]错误：文件 {self.data_file} 格式不正确[/red]")
            exit(1)
    
    def get_random_word(self) -> Dict[str, Any]:
        """获取一个随机单词，优先选择未掌握的单词"""
        unmastered_words = [
            word for word in self.words 
            if not self.progress_data[word['english']]['mastered']
        ]
        
        if unmastered_words:
            return random.choice(unmastered_words)
        else:
            # 如果所有单词都已掌握，随机选择一个
            return random.choice(self.words)
    
    def generate_options(self, correct_word: Dict[str, Any]) -> List[str]:
        """生成4个选项（1个正确答案 + 3个错误选项）"""
        correct_answer = correct_word['chinese']
        
        # 获取其他单词的中文释义作为错误选项
        other_words = [w for w in self.words if w['english'] != correct_word['english']]
        wrong_options = random.sample([w['chinese'] for w in other_words], 3)
        
        # 组合所有选项并打乱顺序
        all_options = [correct_answer] + wrong_options
        random.shuffle(all_options)
        
        return all_options
    
    def display_word_info(self, word: Dict[str, Any]):
        """显示单词信息"""
        # 创建单词信息面板
        word_text = Text()
        word_text.append(f"{word['english']}\n", style="bold blue")
        word_text.append(f"音标: {word['phonetic']}\n", style="cyan")
        word_text.append(f"中文谐音: {word['chinese_phonetic']}", style="yellow")

        word_panel = Panel(
            Align.center(word_text),
            title="[bold green]单词信息[/bold green]",
            border_style="green",
            box=box.ROUNDED
        )

        self.console.print(word_panel)
    
    def display_options(self, options: List[str]) -> int:
        """显示选项并获取用户选择"""
        # 创建选项表格
        table = Table(show_header=False, box=box.SIMPLE)
        table.add_column("选项", style="cyan", width=4)
        table.add_column("释义", style="white")
        
        for i, option in enumerate(options, 1):
            table.add_row(f"{i}.", option)
        
        table.add_row("5.", "[red]不认识这个单词[/red]")
        
        options_panel = Panel(
            table,
            title="[bold yellow]请选择正确的中文释义[/bold yellow]",
            border_style="yellow",
            box=box.ROUNDED
        )
        
        self.console.print(options_panel)
        
        # 获取用户选择
        while True:
            try:
                choice = IntPrompt.ask(
                    "[bold cyan]请输入选项编号 (1-5)[/bold cyan]",
                    choices=["1", "2", "3", "4", "5"]
                )
                return choice
            except KeyboardInterrupt:
                self.console.print("\n[yellow]程序已退出[/yellow]")
                exit(0)
    
    def update_progress(self, word_key: str, is_correct: bool):
        """更新学习进度"""
        progress = self.progress_data[word_key]
        progress['total_attempts'] += 1
        
        if is_correct:
            progress['correct_count'] += 1
            progress['correct_streak'] += 1
            
            # 检查是否达到掌握标准
            if progress['correct_streak'] >= progress['required_streak']:
                progress['mastered'] = True
                self.console.print(f"[bold green]🎉 恭喜！您已掌握单词 '{word_key}'！[/bold green]")
        else:
            progress['correct_streak'] = 0
            # 答错后提高掌握阈值
            if progress['required_streak'] < 5:
                progress['required_streak'] += 1
            progress['mastered'] = False
    
    def display_progress(self):
        """显示学习进度统计"""
        total_words = len(self.words)
        mastered_words = sum(1 for p in self.progress_data.values() if p['mastered'])
        
        # 创建进度条
        with Progress(
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        ) as progress:
            task = progress.add_task("学习进度", total=total_words, completed=mastered_words)
            
            # 创建统计表格
            stats_table = Table(title="学习统计", box=box.ROUNDED)
            stats_table.add_column("项目", style="cyan")
            stats_table.add_column("数值", style="green")
            
            stats_table.add_row("总单词数", str(total_words))
            stats_table.add_row("已掌握", str(mastered_words))
            stats_table.add_row("未掌握", str(total_words - mastered_words))
            stats_table.add_row("掌握率", f"{mastered_words/total_words*100:.1f}%")
            
            self.console.print(stats_table)
    
    def play_round(self):
        """进行一轮学习"""
        # 选择单词
        word = self.get_random_word()
        self.current_word = word
        
        # 清屏
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # 显示进度
        self.display_progress()
        self.console.print()
        
        # 显示单词信息
        self.display_word_info(word)
        self.console.print()
        
        # 生成选项
        options = self.generate_options(word)
        correct_answer = word['chinese']
        correct_index = options.index(correct_answer) + 1
        
        # 显示选项并获取用户选择
        user_choice = self.display_options(options)
        
        # 判断答案
        if user_choice == 5:
            # 用户选择"不认识"
            self.console.print(f"\n[red]您选择了'不认识'[/red]")
            self.console.print(f"[green]正确答案是：{correct_answer}[/green]")
            self.update_progress(word['english'], False)
        elif user_choice == correct_index:
            # 答对了
            self.console.print(f"\n[bold green]✅ 正确！[/bold green]")
            self.update_progress(word['english'], True)
        else:
            # 答错了
            self.console.print(f"\n[red]❌ 错误！[/red]")
            self.console.print(f"[green]正确答案是：{correct_answer}[/green]")
            self.update_progress(word['english'], False)
        
        # 显示当前单词的进度信息
        progress = self.progress_data[word['english']]
        self.console.print(f"\n[cyan]当前单词进度：连续答对 {progress['correct_streak']}/{progress['required_streak']} 次[/cyan]")
        
        # 等待用户按键继续
        Prompt.ask("\n[dim]按回车键继续...[/dim]", default="")
    
    def main_menu(self):
        """主菜单"""
        while True:
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # 显示标题
            title = Text("🎓 交互式单词记忆程序", style="bold magenta", justify="center")
            title_panel = Panel(title, box=box.DOUBLE)
            self.console.print(title_panel)
            self.console.print()
            
            # 显示进度
            self.display_progress()
            self.console.print()
            
            # 显示菜单选项
            menu_table = Table(show_header=False, box=box.SIMPLE)
            menu_table.add_column("选项", style="cyan", width=4)
            menu_table.add_column("功能", style="white")
            
            menu_table.add_row("1.", "开始学习")
            menu_table.add_row("2.", "查看详细进度")
            menu_table.add_row("3.", "重置进度")
            menu_table.add_row("4.", "退出程序")
            
            menu_panel = Panel(
                menu_table,
                title="[bold blue]主菜单[/bold blue]",
                border_style="blue",
                box=box.ROUNDED
            )
            
            self.console.print(menu_panel)
            
            try:
                choice = IntPrompt.ask(
                    "[bold cyan]请选择功能 (1-4)[/bold cyan]",
                    choices=["1", "2", "3", "4"]
                )
                
                if choice == 1:
                    # 开始学习
                    while True:
                        self.play_round()
                        
                        # 检查是否所有单词都已掌握
                        if all(p['mastered'] for p in self.progress_data.values()):
                            self.console.print("\n[bold green]🎉 恭喜！您已掌握所有单词！[/bold green]")
                            Prompt.ask("[dim]按回车键返回主菜单...[/dim]", default="")
                            break
                        
                        # 询问是否继续
                        continue_choice = Prompt.ask(
                            "\n[yellow]是否继续学习？(y/n)[/yellow]",
                            choices=["y", "n", "Y", "N"],
                            default="y"
                        )
                        if continue_choice.lower() == 'n':
                            break
                
                elif choice == 2:
                    # 查看详细进度
                    self.show_detailed_progress()
                
                elif choice == 3:
                    # 重置进度
                    self.reset_progress()
                
                elif choice == 4:
                    # 退出程序
                    self.console.print("[yellow]感谢使用！再见！[/yellow]")
                    break
                    
            except KeyboardInterrupt:
                self.console.print("\n[yellow]程序已退出[/yellow]")
                break
    
    def show_detailed_progress(self):
        """显示详细进度"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # 创建详细进度表格
        table = Table(title="详细学习进度", box=box.ROUNDED)
        table.add_column("单词", style="cyan", width=20)
        table.add_column("状态", style="green", width=8)
        table.add_column("连续答对", style="yellow", width=10)
        table.add_column("总尝试", style="blue", width=8)
        table.add_column("正确率", style="magenta", width=8)
        
        for word in self.words[:20]:  # 只显示前20个单词
            word_key = word['english']
            progress = self.progress_data[word_key]
            
            status = "✅ 已掌握" if progress['mastered'] else "📚 学习中"
            streak = f"{progress['correct_streak']}/{progress['required_streak']}"
            attempts = str(progress['total_attempts'])
            accuracy = f"{progress['correct_count']/max(1, progress['total_attempts'])*100:.0f}%" if progress['total_attempts'] > 0 else "0%"
            
            table.add_row(word_key, status, streak, attempts, accuracy)
        
        self.console.print(table)
        
        if len(self.words) > 20:
            self.console.print(f"\n[dim]注：仅显示前20个单词，总共{len(self.words)}个单词[/dim]")
        
        Prompt.ask("\n[dim]按回车键返回主菜单...[/dim]", default="")
    
    def reset_progress(self):
        """重置学习进度"""
        confirm = Prompt.ask(
            "[red]确定要重置所有学习进度吗？(y/n)[/red]",
            choices=["y", "n", "Y", "N"],
            default="n"
        )
        
        if confirm.lower() == 'y':
            for word_key in self.progress_data:
                self.progress_data[word_key] = {
                    'correct_streak': 0,
                    'required_streak': 2,
                    'total_attempts': 0,
                    'correct_count': 0,
                    'mastered': False
                }
            self.console.print("[green]学习进度已重置！[/green]")
        else:
            self.console.print("[yellow]取消重置[/yellow]")
        
        Prompt.ask("[dim]按回车键返回主菜单...[/dim]", default="")

def main():
    """主函数"""
    app = WordMemoryApp()
    app.main_menu()

if __name__ == "__main__":
    main()
