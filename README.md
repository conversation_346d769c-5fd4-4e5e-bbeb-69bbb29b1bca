# 🎓 交互式单词记忆程序

一个基于Python和rich库开发的交互式单词记忆程序，帮助您高效学习集成电路专业英语词汇。

## ✨ 功能特点

### 核心功能
- **单词展示**：显示英文单词、音标和中文谐音
- **选择题模式**：提供4个中文释义选项供选择
- **不认识选项**：包含"不认识"选项，诚实记录学习状态
- **智能进度算法**：
  - 必须连续答对2次才算掌握单词
  - 答错的单词会提高掌握阈值（最多需要连续答对5次）
  - "不认识"选项视为答错处理

### 交互方式
- **键盘输入**：输入数字1-4选择选项，5选择"不认识"
- **美观界面**：使用rich库创建美观的终端界面
- **实时反馈**：即时显示答题结果和进度信息

### 学习统计
- **进度跟踪**：实时显示学习进度和掌握率
- **详细统计**：查看每个单词的学习状态和正确率
- **进度重置**：支持重置学习进度重新开始

## 🚀 安装和运行

### 环境要求
- Python 3.7+
- 支持conda环境管理

### 安装步骤

1. **克隆或下载项目文件**
   ```bash
   # 确保以下文件在同一目录下：
   # - word_memory_app.py
   # - vocabulary_data.json
   # - requirements.txt
   ```

2. **创建conda环境（推荐）**
   ```bash
   conda create -n word_memory python=3.9
   conda activate word_memory
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **运行程序**
   ```bash
   python word_memory_app.py
   ```

## 📖 使用说明

### 主菜单选项
1. **开始学习**：进入学习模式，开始记忆单词
2. **查看详细进度**：查看每个单词的学习状态
3. **重置进度**：清除所有学习记录，重新开始
4. **退出程序**：退出应用

### 学习模式
1. 程序会显示一个英文单词及其音标和中文谐音
2. 从4个中文释义选项中选择正确答案
3. 如果不认识单词，选择选项5"不认识"
4. 程序会显示答题结果和当前单词的学习进度
5. 按回车键继续下一个单词

### 进度算法
- **初始状态**：每个单词需要连续答对2次才算掌握
- **答对**：连续答对次数+1，达到要求次数后标记为已掌握
- **答错或不认识**：
  - 连续答对次数重置为0
  - 掌握要求次数+1（最多5次）
  - 单词状态重置为未掌握

## 📊 数据格式

程序使用`vocabulary_data.json`文件存储单词数据，格式如下：

```json
[
  {
    "english": "Abrupt junction",
    "chinese": "突变结",
    "phonetic": "/əˈbrʌpt ˈdʒʌŋkʃən/",
    "chinese_phonetic": "额不软普特 涨可神"
  }
]
```

每个单词条目包含：
- `english`：英文单词或短语
- `chinese`：中文释义
- `phonetic`：国际音标
- `chinese_phonetic`：中文谐音

## 🎯 学习建议

1. **诚实选择**：遇到不认识的单词请选择"不认识"，这样有助于程序更好地跟踪您的学习进度
2. **持续学习**：建议每天学习一定数量的单词，保持学习连续性
3. **复习巩固**：程序会优先选择未掌握的单词，确保重点复习薄弱环节
4. **查看进度**：定期查看详细进度，了解自己的学习状况

## 🔧 技术特性

- **智能选词**：优先选择未掌握的单词进行练习
- **随机选项**：每次生成的错误选项都是随机的，避免记忆选项位置
- **进度持久化**：学习进度在程序运行期间保持，重启程序后需要重新开始
- **美观界面**：使用rich库创建彩色、格式化的终端界面
- **错误处理**：包含完善的错误处理机制，确保程序稳定运行

## 📝 注意事项

- 程序运行时学习进度保存在内存中，关闭程序后进度会丢失
- 确保`vocabulary_data.json`文件与程序在同一目录下
- 建议在支持UTF-8编码的终端中运行程序以正确显示中文字符

## 🤝 贡献

欢迎提出改进建议或报告问题！

## 📄 许可证

本项目仅供学习和个人使用。
